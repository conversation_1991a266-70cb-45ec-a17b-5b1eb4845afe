import React, { useState, useMemo } from 'react';
import { useTheme } from '../../../hooks/useTheme';
import { BaseView } from '../BaseView';
import type { BaseViewProps } from '../BaseView';
import type { CalendarViewConfig } from '../../../types/views';
import { cn } from '../../../utils/cn';

export interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end?: Date;
  color?: string;
  data: any;
}

export interface CalendarViewProps extends BaseViewProps {
  config: CalendarViewConfig;
  currentDate?: Date;
  viewMode?: 'month' | 'week' | 'day' | 'agenda';
  onDateChange?: (date: Date) => void;
  onViewModeChange?: (mode: 'month' | 'week' | 'day' | 'agenda') => void;
  onEventClick?: (event: CalendarEvent) => void;
  onDateClick?: (date: Date) => void;
  renderEvent?: (event: CalendarEvent) => React.ReactNode;
  renderDayHeader?: (date: Date) => React.ReactNode;
}

export const CalendarView: React.FC<CalendarViewProps> = ({
  data,
  config,
  currentDate = new Date(),
  viewMode = config.defaultViewMode,
  onDateChange,
  onViewModeChange,
  onEventClick,
  onDateClick,
  renderEvent,
  renderDayHeader,
  className,
  ...baseProps
}) => {
  const { colors } = useTheme();
  const [selectedDate, setSelectedDate] = useState(currentDate);

  // Convert data to calendar events
  const events = useMemo((): CalendarEvent[] => {
    return data.map((item, index) => ({
      id: item.id || index.toString(),
      title: item[config.titleField] || 'Untitled',
      start: new Date(item[config.dateField]),
      end: config.endDateField ? new Date(item[config.endDateField]) : undefined,
      color: config.colorField ? item[config.colorField] : colors.primary,
      data: item,
    }));
  }, [data, config, colors.primary]);

  // Get calendar grid for month view
  const getMonthGrid = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());
    
    const grid: Date[][] = [];
    let currentWeek: Date[] = [];
    
    for (let i = 0; i < 42; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);
      
      currentWeek.push(currentDate);
      
      if (currentWeek.length === 7) {
        grid.push(currentWeek);
        currentWeek = [];
      }
    }
    
    return grid;
  };

  // Get events for a specific date
  const getEventsForDate = (date: Date): CalendarEvent[] => {
    return events.filter(event => {
      const eventDate = new Date(event.start);
      return eventDate.toDateString() === date.toDateString();
    });
  };

  // Navigation handlers
  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(selectedDate);
    
    switch (viewMode) {
      case 'month':
        newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
        break;
      case 'week':
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
        break;
      case 'day':
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
        break;
    }
    
    setSelectedDate(newDate);
    onDateChange?.(newDate);
  };

  const renderMonthView = () => {
    const grid = getMonthGrid(selectedDate);
    const monthName = selectedDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });

    return (
      <div className="flex flex-col h-full">
        {/* Month Header */}
        <div className="flex items-center justify-between p-4 border-b" style={{ borderBottomColor: colors.border }}>
          <h2 className="text-xl font-semibold" style={{ color: colors.text }}>
            {monthName}
          </h2>
          <div className="flex space-x-2">
            <button
              onClick={() => navigateDate('prev')}
              className="p-2 hover:bg-gray-100 rounded"
              style={{ color: colors.text }}
            >
              ←
            </button>
            <button
              onClick={() => navigateDate('next')}
              className="p-2 hover:bg-gray-100 rounded"
              style={{ color: colors.text }}
            >
              →
            </button>
          </div>
        </div>

        {/* Days of week header */}
        <div className="grid grid-cols-7 border-b" style={{ borderBottomColor: colors.border }}>
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
            <div key={day} className="p-2 text-center font-medium" style={{ color: colors.textSecondary }}>
              {day}
            </div>
          ))}
        </div>

        {/* Calendar grid */}
        <div className="flex-1 grid grid-rows-6">
          {grid.map((week, weekIndex) => (
            <div key={weekIndex} className="grid grid-cols-7 border-b" style={{ borderBottomColor: colors.border }}>
              {week.map((date, dayIndex) => {
                const dayEvents = getEventsForDate(date);
                const isCurrentMonth = date.getMonth() === selectedDate.getMonth();
                const isToday = date.toDateString() === new Date().toDateString();

                return (
                  <div
                    key={dayIndex}
                    className={cn(
                      'p-2 border-r min-h-[100px] cursor-pointer hover:bg-gray-50',
                      !isCurrentMonth && 'text-gray-400',
                      isToday && 'bg-blue-50'
                    )}
                    style={{ borderRightColor: colors.border }}
                    onClick={() => onDateClick?.(date)}
                  >
                    <div className="flex justify-between items-start mb-1">
                      <span className={cn('text-sm', isToday && 'font-bold')} style={{ color: colors.text }}>
                        {date.getDate()}
                      </span>
                    </div>
                    <div className="space-y-1">
                      {dayEvents.slice(0, 3).map(event => (
                        <div
                          key={event.id}
                          className="text-xs p-1 rounded truncate cursor-pointer"
                          style={{ backgroundColor: event.color, color: 'white' }}
                          onClick={(e) => {
                            e.stopPropagation();
                            onEventClick?.(event);
                          }}
                        >
                          {renderEvent ? renderEvent(event) : event.title}
                        </div>
                      ))}
                      {dayEvents.length > 3 && (
                        <div className="text-xs text-gray-500">
                          +{dayEvents.length - 3} more
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderAgendaView = () => {
    const sortedEvents = [...events].sort((a, b) => a.start.getTime() - b.start.getTime());

    return (
      <div className="space-y-4 p-4">
        {sortedEvents.map(event => (
          <div
            key={event.id}
            className="flex items-center space-x-4 p-4 rounded-lg border cursor-pointer hover:shadow-md"
            style={{ backgroundColor: colors.surface, borderColor: colors.border }}
            onClick={() => onEventClick?.(event)}
          >
            <div
              className="w-4 h-4 rounded-full"
              style={{ backgroundColor: event.color }}
            />
            <div className="flex-1">
              <h3 className="font-medium" style={{ color: colors.text }}>
                {event.title}
              </h3>
              <p className="text-sm" style={{ color: colors.textSecondary }}>
                {event.start.toLocaleDateString()} at {event.start.toLocaleTimeString()}
              </p>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderViewModeSelector = () => (
    <div className="flex space-x-1 p-1 bg-gray-100 rounded-lg">
      {config.viewModes.map(mode => (
        <button
          key={mode}
          onClick={() => onViewModeChange?.(mode)}
          className={cn(
            'px-3 py-1 text-sm rounded transition-colors',
            viewMode === mode ? 'bg-white shadow-sm' : 'hover:bg-gray-200'
          )}
          style={{ 
            color: viewMode === mode ? colors.text : colors.textSecondary 
          }}
        >
          {mode.charAt(0).toUpperCase() + mode.slice(1)}
        </button>
      ))}
    </div>
  );

  return (
    <BaseView {...baseProps} data={data} className={className}>
      <div className="flex flex-col h-full">
        {/* View mode selector */}
        <div className="flex justify-between items-center p-4 border-b" style={{ borderBottomColor: colors.border }}>
          <div />
          {renderViewModeSelector()}
        </div>

        {/* Calendar content */}
        <div className="flex-1">
          {viewMode === 'month' && renderMonthView()}
          {viewMode === 'agenda' && renderAgendaView()}
          {/* TODO: Implement week and day views */}
        </div>
      </div>
    </BaseView>
  );
};

export default CalendarView;
