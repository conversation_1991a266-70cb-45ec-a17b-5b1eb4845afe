import React from 'react';
import type { ReactNode } from 'react';
import { useTheme } from '../../../hooks/useTheme';
import type { ViewContext, ViewState } from '../../../types/views';
import { cn } from '../../../utils/cn';

export interface BaseViewProps {
  data: any[];
  loading?: boolean;
  error?: string;
  className?: string;
  'data-testid'?: string;
  children?: ReactNode;
  context?: ViewContext;
  state?: Partial<ViewState>;
  onItemClick?: (item: any, index: number) => void;
  onItemSelect?: (item: any, selected: boolean) => void;
  onItemDoubleClick?: (item: any, index: number) => void;
  renderEmpty?: () => ReactNode;
  renderError?: (error: string) => ReactNode;
  renderLoading?: () => ReactNode;
}

export const BaseView: React.FC<BaseViewProps> = ({
  data,
  loading = false,
  error,
  className,
  'data-testid': testId,
  children,
  renderEmpty,
  renderError,
  renderLoading,
}) => {
  const { colors } = useTheme();

  // Loading state
  if (loading) {
    if (renderLoading) {
      return <div data-testid={testId}>{renderLoading()}</div>;
    }
    return (
      <div 
        className={cn('flex items-center justify-center p-8', className)}
        style={{ backgroundColor: colors.background }}
        data-testid={testId}
      >
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2" style={{ borderColor: colors.primary }} />
          <span style={{ color: colors.textSecondary }}>Loading...</span>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    if (renderError) {
      return <div data-testid={testId}>{renderError(error)}</div>;
    }
    return (
      <div 
        className={cn('flex items-center justify-center p-8', className)}
        style={{ backgroundColor: colors.background }}
        data-testid={testId}
      >
        <div className="text-center">
          <div className="text-red-500 text-2xl mb-2">⚠️</div>
          <h3 className="text-lg font-semibold mb-2" style={{ color: colors.text }}>
            Error Loading Data
          </h3>
          <p className="text-sm" style={{ color: colors.textSecondary }}>
            {error}
          </p>
        </div>
      </div>
    );
  }

  // Empty state
  if (!data || data.length === 0) {
    if (renderEmpty) {
      return <div data-testid={testId}>{renderEmpty()}</div>;
    }
    return (
      <div 
        className={cn('flex items-center justify-center p-8', className)}
        style={{ backgroundColor: colors.background }}
        data-testid={testId}
      >
        <div className="text-center">
          <div className="text-4xl mb-4" style={{ color: colors.textSecondary }}>📋</div>
          <h3 className="text-lg font-semibold mb-2" style={{ color: colors.text }}>
            No Data Available
          </h3>
          <p className="text-sm" style={{ color: colors.textSecondary }}>
            There are no items to display in this view.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={cn('w-full', className)}
      style={{ backgroundColor: colors.background }}
      data-testid={testId}
    >
      {children}
    </div>
  );
};

export default BaseView;
