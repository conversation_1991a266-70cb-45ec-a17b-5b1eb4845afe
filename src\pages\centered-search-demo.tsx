import React, { useState } from 'react';
import { CenteredSearchChipInput, ChipData, FilterOption } from '../components/ui';
import { useThemeStore } from '../stores/themeStore';

const CenteredSearchDemo: React.FC = () => {
  const { colors } = useThemeStore();
  const [chips, setChips] = useState<ChipData[]>([
    { id: '1', label: 'Undelivered Complete', type: 'favorite', removable: true },
    { id: '2', label: 'Order', type: 'default', removable: true },
    { id: '3', label: 'pinan', type: 'default', removable: true },
  ]);

  const filterOptions: FilterOption[] = [
    // Default Filters
    { id: 'status', label: 'Status', category: 'default', type: 'select' },
    { id: 'priority', label: 'Priority', category: 'default', type: 'select' },
    { id: 'department', label: 'Department', category: 'default', type: 'select' },
    { id: 'date-range', label: 'Date Range', category: 'default', type: 'date' },
    { id: 'my-quotations', label: 'My Quotations', category: 'default' },
    { id: 'quotations', label: 'Quotations', category: 'default' },
    { id: 'sales-orders', label: 'Sales Orders', category: 'default' },
    { id: 'create-date', label: 'Create Date', category: 'default', type: 'date' },
    
    // Custom Filters
    { id: 'custom-field-1', label: 'Custom Field 1', category: 'custom', type: 'text' },
    { id: 'custom-field-2', label: 'Custom Field 2', category: 'custom', type: 'number' },
    { id: 'tags', label: 'Tags', category: 'custom', type: 'select' },
    { id: 'add-custom-filter', label: 'Add Custom Filter', category: 'custom' },
    
    // Group By Options
    { id: 'group-salesperson', label: 'Salesperson', category: 'group' },
    { id: 'group-customer', label: 'Customer', category: 'group' },
    { id: 'group-order-date', label: 'Order Date', category: 'group' },
    { id: 'group-region', label: 'Region', category: 'group' },
    { id: 'add-custom-group', label: 'Add Custom Group', category: 'group' },
    
    // Favorites
    { id: 'fav-fully-invoiced', label: 'Fully Invoiced', category: 'favorite' },
    { id: 'fav-quotations', label: 'Quotations', category: 'favorite' },
    { id: 'fav-undelivered-complete', label: 'Undelivered Complete', category: 'favorite' },
    { id: 'fav-unpaid-orders', label: 'Unpaid Orders', category: 'favorite' },
    { id: 'save-current-search', label: 'Save current search', category: 'favorite' },
  ];

  const handleChipAdd = (chip: ChipData) => {
    setChips(prev => [...prev, chip]);
  };

  const handleChipRemove = (chipId: string) => {
    setChips(prev => prev.filter(chip => chip.id !== chipId));
  };

  const handleClearAll = () => {
    setChips([]);
  };

  const handleSearch = (query: string) => {
    console.log('Search query:', query);
  };

  return (
    <div 
      className="min-h-screen p-8"
      style={{ backgroundColor: colors.background }}
    >
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 
            className="text-3xl font-bold mb-2"
            style={{ color: colors.text }}
          >
            Centered Search Chip Input Demo
          </h1>
          <p 
            className="text-lg"
            style={{ color: colors.textSecondary }}
          >
            A comprehensive search input with integrated filter chips, supporting default filters, custom filters, grouping, and favorites.
          </p>
        </div>

        {/* Main Demo */}
        <div className="mb-12">
          <h2 
            className="text-xl font-semibold mb-4"
            style={{ color: colors.text }}
          >
            Interactive Demo
          </h2>
          <CenteredSearchChipInput
            placeholder="Search..."
            chips={chips}
            filterOptions={filterOptions}
            onChipAdd={handleChipAdd}
            onChipRemove={handleChipRemove}
            onClearAll={handleClearAll}
            onSearch={handleSearch}
          />
        </div>

        {/* Features Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {[
            {
              title: 'Default Filters',
              description: 'Pre-defined filters like Status, Priority, Department, and Date Range',
              icon: '🔍',
            },
            {
              title: 'Custom Filters',
              description: 'User-defined custom fields and filters for specific needs',
              icon: '⚙️',
            },
            {
              title: 'Group By',
              description: 'Organize data by Salesperson, Customer, Order Date, or custom groups',
              icon: '📊',
            },
            {
              title: 'Favorites',
              description: 'Quick access to frequently used searches and saved filters',
              icon: '⭐',
            },
          ].map((feature, index) => (
            <div
              key={index}
              className="p-6 rounded-lg border"
              style={{
                backgroundColor: colors.surface,
                borderColor: colors.border,
              }}
            >
              <div className="text-2xl mb-3">{feature.icon}</div>
              <h3 
                className="text-lg font-semibold mb-2"
                style={{ color: colors.text }}
              >
                {feature.title}
              </h3>
              <p 
                className="text-sm"
                style={{ color: colors.textSecondary }}
              >
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        {/* Debug Info */}
        <div 
          className="p-6 rounded-lg border"
          style={{
            backgroundColor: colors.surface,
            borderColor: colors.border,
          }}
        >
          <h3 
            className="text-lg font-semibold mb-4"
            style={{ color: colors.text }}
          >
            Debug Information
          </h3>
          <div className="space-y-2">
            <p style={{ color: colors.textSecondary }}>
              <strong>Active Chips:</strong> {chips.length}
            </p>
            <div>
              <strong style={{ color: colors.textSecondary }}>Chips:</strong>
              <ul className="list-disc list-inside ml-4 mt-1">
                {chips.map(chip => (
                  <li key={chip.id} style={{ color: colors.textMuted }}>
                    {chip.label} ({chip.type})
                  </li>
                ))}
              </ul>
            </div>
            <p style={{ color: colors.textSecondary }}>
              <strong>Available Filter Options:</strong> {filterOptions.length}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CenteredSearchDemo;
