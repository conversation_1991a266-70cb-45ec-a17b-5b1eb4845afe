import React, { useMemo, useState } from 'react';
import { useTheme } from '../../../hooks/useTheme';
import { BaseView } from '../BaseView';
import type { BaseViewProps } from '../BaseView';
import type { RelationshipViewConfig } from '../../../types/views';
import { cn } from '../../../utils/cn';

export interface GraphNode {
  id: string;
  label: string;
  x: number;
  y: number;
  color?: string;
  size?: number;
  data: any;
}

export interface GraphEdge {
  id: string;
  source: string;
  target: string;
  label?: string;
  data: any;
}

export interface RelationshipViewProps extends BaseViewProps {
  config: RelationshipViewConfig;
  onNodeClick?: (node: GraphNode) => void;
  onEdgeClick?: (edge: GraphEdge) => void;
  renderNode?: (node: GraphNode) => React.ReactNode;
  renderEdge?: (edge: GraphEdge) => React.ReactNode;
  width?: number;
  height?: number;
}

export const RelationshipView: React.FC<RelationshipViewProps> = ({
  data,
  config,
  onNodeClick,
  onEdgeClick,
  renderNode,
  renderEdge,
  width = 800,
  height = 600,
  className,
  ...baseProps
}) => {
  const { colors } = useTheme();
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [zoom, setZoom] = useState(1);
  const [pan, setPan] = useState({ x: 0, y: 0 });

  // Process data into nodes and edges
  const { nodes, edges } = useMemo(() => {
    const nodeMap = new Map<string, GraphNode>();
    const edgeList: GraphEdge[] = [];

    // Extract nodes and edges from data
    data.forEach((item, index) => {
      const nodeId = item[config.nodeIdField];
      const nodeLabel = item[config.nodeLabelField];
      
      if (nodeId && !nodeMap.has(nodeId)) {
        nodeMap.set(nodeId, {
          id: nodeId,
          label: nodeLabel || nodeId,
          x: 0, // Will be calculated by layout
          y: 0,
          color: config.nodeColorField ? item[config.nodeColorField] : colors.primary,
          size: config.nodeSizeField ? parseFloat(item[config.nodeSizeField]) || 20 : 20,
          data: item,
        });
      }

      // Check for edges
      if (config.edgeSourceField && config.edgeTargetField) {
        const sourceId = item[config.edgeSourceField];
        const targetId = item[config.edgeTargetField];
        
        if (sourceId && targetId && sourceId !== targetId) {
          edgeList.push({
            id: `${sourceId}-${targetId}-${index}`,
            source: sourceId,
            target: targetId,
            label: config.edgeLabelField ? item[config.edgeLabelField] : undefined,
            data: item,
          });
        }
      }
    });

    const nodeArray = Array.from(nodeMap.values());
    
    // Simple force-directed layout
    applyForceLayout(nodeArray, edgeList, width, height);

    return { nodes: nodeArray, edges: edgeList };
  }, [data, config, colors.primary, width, height]);

  // Simple force-directed layout algorithm
  const applyForceLayout = (nodes: GraphNode[], edges: GraphEdge[], w: number, h: number) => {
    const iterations = 100;
    const k = Math.sqrt((w * h) / nodes.length);
    
    // Initialize positions randomly
    nodes.forEach(node => {
      node.x = Math.random() * w;
      node.y = Math.random() * h;
    });

    for (let iter = 0; iter < iterations; iter++) {
      // Calculate repulsive forces between nodes
      nodes.forEach(node1 => {
        let fx = 0, fy = 0;
        
        nodes.forEach(node2 => {
          if (node1.id !== node2.id) {
            const dx = node1.x - node2.x;
            const dy = node1.y - node2.y;
            const distance = Math.sqrt(dx * dx + dy * dy) || 1;
            const force = (k * k) / distance;
            
            fx += (dx / distance) * force;
            fy += (dy / distance) * force;
          }
        });

        node1.x += fx * 0.1;
        node1.y += fy * 0.1;
      });

      // Calculate attractive forces along edges
      edges.forEach(edge => {
        const source = nodes.find(n => n.id === edge.source);
        const target = nodes.find(n => n.id === edge.target);
        
        if (source && target) {
          const dx = target.x - source.x;
          const dy = target.y - source.y;
          const distance = Math.sqrt(dx * dx + dy * dy) || 1;
          const force = (distance * distance) / k;
          
          const fx = (dx / distance) * force * 0.1;
          const fy = (dy / distance) * force * 0.1;
          
          source.x += fx;
          source.y += fy;
          target.x -= fx;
          target.y -= fy;
        }
      });

      // Keep nodes within bounds
      nodes.forEach(node => {
        node.x = Math.max(50, Math.min(w - 50, node.x));
        node.y = Math.max(50, Math.min(h - 50, node.y));
      });
    }
  };

  const handleNodeClick = (node: GraphNode) => {
    setSelectedNode(node.id === selectedNode ? null : node.id);
    onNodeClick?.(node);
  };

  const renderGraph = () => (
    <div className="relative border rounded-lg overflow-hidden" style={{ borderColor: colors.border }}>
      <svg 
        width={width} 
        height={height}
        className="bg-white"
        viewBox={`${-pan.x} ${-pan.y} ${width / zoom} ${height / zoom}`}
      >
        {/* Edges */}
        <g>
          {edges.map(edge => {
            const source = nodes.find(n => n.id === edge.source);
            const target = nodes.find(n => n.id === edge.target);
            
            if (!source || !target) return null;

            return (
              <g key={edge.id}>
                <line
                  x1={source.x}
                  y1={source.y}
                  x2={target.x}
                  y2={target.y}
                  stroke={colors.border}
                  strokeWidth="2"
                  className="cursor-pointer hover:stroke-blue-500"
                  onClick={() => onEdgeClick?.(edge)}
                />
                {edge.label && config.showLabels && (
                  <text
                    x={(source.x + target.x) / 2}
                    y={(source.y + target.y) / 2}
                    textAnchor="middle"
                    fontSize="12"
                    fill={colors.textSecondary}
                    className="pointer-events-none"
                  >
                    {edge.label}
                  </text>
                )}
              </g>
            );
          })}
        </g>

        {/* Nodes */}
        <g>
          {nodes.map(node => {
            const isSelected = selectedNode === node.id;
            
            return (
              <g key={node.id}>
                <circle
                  cx={node.x}
                  cy={node.y}
                  r={node.size}
                  fill={node.color}
                  stroke={isSelected ? colors.primary : colors.background}
                  strokeWidth={isSelected ? 3 : 2}
                  className="cursor-pointer hover:opacity-80 transition-opacity"
                  onClick={() => handleNodeClick(node)}
                />
                {config.showLabels && (
                  <text
                    x={node.x}
                    y={node.y + node.size + 15}
                    textAnchor="middle"
                    fontSize="12"
                    fill={colors.text}
                    className="pointer-events-none"
                  >
                    {node.label}
                  </text>
                )}
              </g>
            );
          })}
        </g>
      </svg>

      {/* Controls */}
      {config.enableZoom && (
        <div className="absolute top-4 right-4 flex flex-col space-y-2">
          <button
            className="w-8 h-8 bg-white border rounded shadow hover:bg-gray-50 flex items-center justify-center"
            style={{ borderColor: colors.border, color: colors.text }}
            onClick={() => setZoom(Math.min(zoom * 1.2, 3))}
          >
            +
          </button>
          <button
            className="w-8 h-8 bg-white border rounded shadow hover:bg-gray-50 flex items-center justify-center"
            style={{ borderColor: colors.border, color: colors.text }}
            onClick={() => setZoom(Math.max(zoom / 1.2, 0.5))}
          >
            −
          </button>
        </div>
      )}

      {/* Legend */}
      <div 
        className="absolute bottom-4 left-4 bg-white border rounded p-3 shadow-lg"
        style={{ borderColor: colors.border }}
      >
        <h4 className="font-semibold text-sm mb-2" style={{ color: colors.text }}>
          Network Graph
        </h4>
        <div className="space-y-1 text-xs" style={{ color: colors.textSecondary }}>
          <div>{nodes.length} nodes</div>
          <div>{edges.length} connections</div>
        </div>
      </div>
    </div>
  );

  return (
    <BaseView {...baseProps} data={data} className={className}>
      <div className="p-6">
        {nodes.length > 0 ? renderGraph() : (
          <div className="text-center py-12">
            <div className="text-4xl mb-4">🔗</div>
            <h3 className="text-lg font-semibold mb-2" style={{ color: colors.text }}>
              No Relationships Found
            </h3>
            <p className="text-sm" style={{ color: colors.textSecondary }}>
              No valid node or edge data found to display the relationship graph.
            </p>
          </div>
        )}
      </div>
    </BaseView>
  );
};

export default RelationshipView;
