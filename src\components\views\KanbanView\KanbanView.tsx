import React, { useState } from 'react';
import { useTheme } from '../../../hooks/useTheme';
import { BaseView } from '../BaseView';
import type { BaseViewProps } from '../BaseView';
import type { KanbanViewConfig } from '../../../types/views';
import { cn } from '../../../utils/cn';

export interface KanbanColumn {
  id: string;
  title: string;
  color?: string;
  limit?: number;
  isCollapsed?: boolean;
  items: any[];
}

export interface KanbanViewProps extends BaseViewProps {
  config: KanbanViewConfig;
  onItemMove?: (itemId: string, fromColumn: string, toColumn: string, newIndex: number) => void;
  onColumnCollapse?: (columnId: string, collapsed: boolean) => void;
  renderCard: (item: any, index: number) => React.ReactNode;
  renderColumnHeader?: (column: KanbanColumn) => React.ReactNode;
  renderColumnFooter?: (column: KanbanColumn) => React.ReactNode;
  enableDragDrop?: boolean;
}

export const KanbanView: React.FC<KanbanViewProps> = ({
  data,
  config,
  onItemMove,
  onColumnCollapse,
  renderCard,
  renderColumnHeader,
  renderColumnFooter,
  enableDragDrop = true,
  className,
  ...baseProps
}) => {
  const { colors } = useTheme();
  const [draggedItem, setDraggedItem] = useState<any>(null);
  const [dragOverColumn, setDragOverColumn] = useState<string | null>(null);

  // Group data by the configured field
  const groupedData = React.useMemo(() => {
    const groups: { [key: string]: any[] } = {};
    
    // Initialize columns
    config.columns.forEach(col => {
      groups[col.id] = [];
    });

    // Group items
    data.forEach(item => {
      const groupValue = item[config.groupByField];
      if (groups[groupValue]) {
        groups[groupValue].push(item);
      }
    });

    return groups;
  }, [data, config.groupByField, config.columns]);

  const handleDragStart = (e: React.DragEvent, item: any) => {
    if (!enableDragDrop) return;
    setDraggedItem(item);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent, columnId: string) => {
    if (!enableDragDrop) return;
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverColumn(columnId);
  };

  const handleDragLeave = () => {
    setDragOverColumn(null);
  };

  const handleDrop = (e: React.DragEvent, columnId: string) => {
    if (!enableDragDrop || !draggedItem) return;
    e.preventDefault();
    
    const currentColumn = config.columns.find(col => 
      groupedData[col.id].includes(draggedItem)
    );
    
    if (currentColumn && currentColumn.id !== columnId && onItemMove) {
      const newIndex = groupedData[columnId].length;
      onItemMove(draggedItem.id, currentColumn.id, columnId, newIndex);
    }
    
    setDraggedItem(null);
    setDragOverColumn(null);
  };

  const renderColumn = (column: KanbanViewConfig['columns'][0]) => {
    const items = groupedData[column.id] || [];
    const isOverLimit = column.limit && items.length > column.limit;
    const isDragOver = dragOverColumn === column.id;

    return (
      <div
        key={column.id}
        className={cn(
          'flex flex-col min-h-0 rounded-lg border',
          isDragOver && 'ring-2 ring-blue-500 ring-opacity-50'
        )}
        style={{
          backgroundColor: colors.surface,
          borderColor: colors.border,
          minWidth: '280px',
          maxWidth: '320px',
        }}
        onDragOver={(e) => handleDragOver(e, column.id)}
        onDragLeave={handleDragLeave}
        onDrop={(e) => handleDrop(e, column.id)}
      >
        {/* Column Header */}
        <div 
          className="flex items-center justify-between p-4 border-b"
          style={{ borderBottomColor: colors.border }}
        >
          {renderColumnHeader ? renderColumnHeader({
            id: column.id,
            title: column.title,
            color: column.color,
            limit: column.limit,
            isCollapsed: column.isCollapsed,
            items,
          }) : (
            <div className="flex items-center space-x-2">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: column.color || colors.primary }}
              />
              <h3 className="font-semibold" style={{ color: colors.text }}>
                {column.title}
              </h3>
              <span 
                className={cn(
                  'px-2 py-1 text-xs rounded-full',
                  isOverLimit && 'bg-red-100 text-red-800'
                )}
                style={{ 
                  backgroundColor: isOverLimit ? undefined : colors.muted,
                  color: isOverLimit ? undefined : colors.textSecondary 
                }}
              >
                {items.length}
                {column.limit && ` / ${column.limit}`}
              </span>
              {onColumnCollapse && (
                <button
                  onClick={() => onColumnCollapse(column.id, !column.isCollapsed)}
                  className="p-1 hover:bg-gray-100 rounded"
                >
                  {column.isCollapsed ? '▶' : '▼'}
                </button>
              )}
            </div>
          )}
        </div>

        {/* Column Content */}
        {!column.isCollapsed && (
          <div className="flex-1 p-4 space-y-3 overflow-y-auto">
            {items.map((item, index) => (
              <div
                key={item.id || index}
                draggable={enableDragDrop}
                onDragStart={(e) => handleDragStart(e, item)}
                className={cn(
                  'rounded-lg border transition-all duration-200',
                  enableDragDrop && 'cursor-move hover:shadow-md',
                  draggedItem === item && 'opacity-50'
                )}
                style={{
                  backgroundColor: colors.background,
                  borderColor: colors.border,
                }}
              >
                {renderCard(item, index)}
              </div>
            ))}
            
            {/* Drop zone indicator */}
            {isDragOver && (
              <div 
                className="border-2 border-dashed rounded-lg p-4 text-center"
                style={{ borderColor: colors.primary }}
              >
                <span style={{ color: colors.textSecondary }}>
                  Drop item here
                </span>
              </div>
            )}
          </div>
        )}

        {/* Column Footer */}
        {renderColumnFooter && (
          <div 
            className="p-4 border-t"
            style={{ borderTopColor: colors.border }}
          >
            {renderColumnFooter({
              id: column.id,
              title: column.title,
              color: column.color,
              limit: column.limit,
              isCollapsed: column.isCollapsed,
              items,
            })}
          </div>
        )}
      </div>
    );
  };

  return (
    <BaseView {...baseProps} data={data} className={className}>
      <div className="flex space-x-4 overflow-x-auto pb-4">
        {config.columns.map(renderColumn)}
      </div>
    </BaseView>
  );
};

export default KanbanView;
