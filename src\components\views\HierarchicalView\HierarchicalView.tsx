import React, { useState, useMemo } from 'react';
import { useTheme } from '../../../hooks/useTheme';
import { BaseView } from '../BaseView';
import type { BaseViewProps } from '../BaseView';
import type { HierarchicalViewConfig } from '../../../types/views';
import { cn } from '../../../utils/cn';

export interface TreeNode {
  id: string;
  label: string;
  icon?: React.ReactNode;
  children: TreeNode[];
  parent?: TreeNode;
  level: number;
  isExpanded: boolean;
  data: any;
}

export interface HierarchicalViewProps extends BaseViewProps {
  config: HierarchicalViewConfig;
  expandedNodes?: string[];
  onNodeExpand?: (nodeId: string, expanded: boolean) => void;
  onNodeClick?: (node: TreeNode) => void;
  onNodeSelect?: (node: TreeNode, selected: boolean) => void;
  selectedNodes?: string[];
  renderNode?: (node: TreeNode) => React.ReactNode;
  renderNodeIcon?: (node: TreeNode) => React.ReactNode;
  enableDragDrop?: boolean;
  onNodeMove?: (nodeId: string, newParentId: string, newIndex: number) => void;
}

export const HierarchicalView: React.FC<HierarchicalViewProps> = ({
  data,
  config,
  expandedNodes = [],
  onNodeExpand,
  onNodeClick,
  onNodeSelect,
  selectedNodes = [],
  renderNode,
  renderNodeIcon,
  enableDragDrop = config.allowDragDrop ?? false,
  onNodeMove,
  className,
  ...baseProps
}) => {
  const { colors } = useTheme();
  const [internalExpanded, setInternalExpanded] = useState<Set<string>>(
    new Set(expandedNodes)
  );
  const [draggedNode, setDraggedNode] = useState<TreeNode | null>(null);
  const [dragOverNode, setDragOverNode] = useState<string | null>(null);

  // Build tree structure from flat data
  const treeData = useMemo((): TreeNode[] => {
    const nodeMap = new Map<string, TreeNode>();
    const rootNodes: TreeNode[] = [];

    // First pass: create all nodes
    data.forEach(item => {
      const node: TreeNode = {
        id: item[config.idField],
        label: item[config.labelField],
        icon: config.iconField ? item[config.iconField] : undefined,
        children: [],
        level: 0,
        isExpanded: config.expandedByDefault ?? false,
        data: item,
      };
      nodeMap.set(node.id, node);
    });

    // Second pass: build hierarchy
    data.forEach(item => {
      const node = nodeMap.get(item[config.idField]);
      const parentId = item[config.parentField];
      
      if (node) {
        if (parentId && nodeMap.has(parentId)) {
          const parent = nodeMap.get(parentId)!;
          parent.children.push(node);
          node.parent = parent;
          node.level = parent.level + 1;
        } else {
          rootNodes.push(node);
        }
      }
    });

    // Set expansion state
    const expandedSet = onNodeExpand ? new Set(expandedNodes) : internalExpanded;
    const setExpansion = (nodes: TreeNode[]) => {
      nodes.forEach(node => {
        node.isExpanded = expandedSet.has(node.id);
        setExpansion(node.children);
      });
    };
    setExpansion(rootNodes);

    return rootNodes;
  }, [data, config, expandedNodes, internalExpanded, onNodeExpand]);

  const handleNodeExpand = (node: TreeNode) => {
    if (onNodeExpand) {
      onNodeExpand(node.id, !node.isExpanded);
    } else {
      const newExpanded = new Set(internalExpanded);
      if (node.isExpanded) {
        newExpanded.delete(node.id);
      } else {
        newExpanded.add(node.id);
      }
      setInternalExpanded(newExpanded);
    }
  };

  const handleDragStart = (e: React.DragEvent, node: TreeNode) => {
    if (!enableDragDrop) return;
    setDraggedNode(node);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent, node: TreeNode) => {
    if (!enableDragDrop || !draggedNode) return;
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverNode(node.id);
  };

  const handleDragLeave = () => {
    setDragOverNode(null);
  };

  const handleDrop = (e: React.DragEvent, targetNode: TreeNode) => {
    if (!enableDragDrop || !draggedNode || !onNodeMove) return;
    e.preventDefault();
    
    // Prevent dropping on self or descendants
    if (draggedNode.id === targetNode.id || isDescendant(draggedNode, targetNode)) {
      setDraggedNode(null);
      setDragOverNode(null);
      return;
    }
    
    const newIndex = targetNode.children.length;
    onNodeMove(draggedNode.id, targetNode.id, newIndex);
    
    setDraggedNode(null);
    setDragOverNode(null);
  };

  const isDescendant = (ancestor: TreeNode, node: TreeNode): boolean => {
    let current = node.parent;
    while (current) {
      if (current.id === ancestor.id) return true;
      current = current.parent;
    }
    return false;
  };

  const renderTreeNode = (node: TreeNode): React.ReactNode => {
    const hasChildren = node.children.length > 0;
    const isSelected = selectedNodes.includes(node.id);
    const isDragOver = dragOverNode === node.id;
    const maxDepth = config.maxDepth;

    if (maxDepth && node.level >= maxDepth) {
      return null;
    }

    return (
      <div key={node.id} className="select-none">
        {/* Node row */}
        <div
          className={cn(
            'flex items-center py-1 px-2 rounded hover:bg-gray-50 transition-colors',
            isSelected && 'bg-blue-50 dark:bg-blue-900/20',
            isDragOver && 'bg-blue-100 dark:bg-blue-800/30',
            onNodeClick && 'cursor-pointer',
            enableDragDrop && 'cursor-move'
          )}
          style={{
            paddingLeft: `${node.level * 20 + 8}px`,
            backgroundColor: isSelected ? colors.muted : undefined,
          }}
          draggable={enableDragDrop}
          onDragStart={(e) => handleDragStart(e, node)}
          onDragOver={(e) => handleDragOver(e, node)}
          onDragLeave={handleDragLeave}
          onDrop={(e) => handleDrop(e, node)}
          onClick={() => onNodeClick?.(node)}
        >
          {/* Expand/collapse button */}
          <div className="w-4 h-4 flex items-center justify-center mr-1">
            {hasChildren && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleNodeExpand(node);
                }}
                className="w-4 h-4 flex items-center justify-center hover:bg-gray-200 rounded"
                style={{ color: colors.textSecondary }}
              >
                {node.isExpanded ? '▼' : '▶'}
              </button>
            )}
          </div>

          {/* Selection checkbox */}
          {onNodeSelect && (
            <input
              type="checkbox"
              className="mr-2"
              checked={isSelected}
              onChange={(e) => {
                e.stopPropagation();
                onNodeSelect(node, e.target.checked);
              }}
            />
          )}

          {/* Node icon */}
          <div className="w-4 h-4 mr-2 flex items-center justify-center">
            {renderNodeIcon ? renderNodeIcon(node) : (
              node.icon || (
                <div
                  className="w-2 h-2 rounded-full"
                  style={{ backgroundColor: colors.primary }}
                />
              )
            )}
          </div>

          {/* Node content */}
          <div className="flex-1 min-w-0">
            {renderNode ? renderNode(node) : (
              <span className="truncate" style={{ color: colors.text }}>
                {node.label}
              </span>
            )}
          </div>

          {/* Connection lines */}
          {config.showLines && node.level > 0 && (
            <div
              className="absolute border-l border-t"
              style={{
                left: `${(node.level - 1) * 20 + 16}px`,
                top: '50%',
                width: '12px',
                height: '1px',
                borderColor: colors.border,
              }}
            />
          )}
        </div>

        {/* Children */}
        {hasChildren && node.isExpanded && (
          <div>
            {node.children.map(child => renderTreeNode(child))}
          </div>
        )}
      </div>
    );
  };

  return (
    <BaseView {...baseProps} data={data} className={className}>
      <div className="p-4 relative">
        {treeData.map(node => renderTreeNode(node))}
      </div>
    </BaseView>
  );
};

export default HierarchicalView;
