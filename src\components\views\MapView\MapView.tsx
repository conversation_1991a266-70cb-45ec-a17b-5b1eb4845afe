import React, { useMemo } from 'react';
import { useTheme } from '../../../hooks/useTheme';
import { BaseView } from '../BaseView';
import type { BaseViewProps } from '../BaseView';
import type { MapViewConfig } from '../../../types/views';
import { cn } from '../../../utils/cn';

export interface MapMarker {
  id: string;
  latitude: number;
  longitude: number;
  title: string;
  description?: string;
  color?: string;
  size?: number;
  data: any;
}

export interface MapViewProps extends BaseViewProps {
  config: MapViewConfig;
  center?: [number, number];
  zoom?: number;
  onMarkerClick?: (marker: MapMarker) => void;
  onMapClick?: (latitude: number, longitude: number) => void;
  renderMarker?: (marker: MapMarker) => React.ReactNode;
  renderTooltip?: (marker: MapMarker) => React.ReactNode;
}

export const MapView: React.FC<MapViewProps> = ({
  data,
  config,
  center = config.defaultCenter || [0, 0],
  zoom = config.defaultZoom || 10,
  onMarkerClick,
  onMapClick,
  renderMarker,
  renderTooltip,
  className,
  ...baseProps
}) => {
  const { colors } = useTheme();

  // Convert data to map markers
  const markers = useMemo((): MapMarker[] => {
    return data
      .filter(item => 
        item[config.latitudeField] != null && 
        item[config.longitudeField] != null
      )
      .map((item, index) => ({
        id: item.id || index.toString(),
        latitude: parseFloat(item[config.latitudeField]),
        longitude: parseFloat(item[config.longitudeField]),
        title: item[config.titleField] || 'Untitled',
        description: config.descriptionField ? item[config.descriptionField] : undefined,
        color: config.colorField ? item[config.colorField] : colors.primary,
        size: config.sizeField ? parseFloat(item[config.sizeField]) || 10 : 10,
        data: item,
      }));
  }, [data, config, colors.primary]);

  // Simple map bounds calculation
  const bounds = useMemo(() => {
    if (markers.length === 0) return null;

    const lats = markers.map(m => m.latitude);
    const lngs = markers.map(m => m.longitude);

    return {
      north: Math.max(...lats),
      south: Math.min(...lats),
      east: Math.max(...lngs),
      west: Math.min(...lngs),
    };
  }, [markers]);

  // Convert lat/lng to pixel coordinates (simplified projection)
  const projectToPixels = (lat: number, lng: number, mapWidth: number, mapHeight: number) => {
    if (!bounds) return { x: 0, y: 0 };

    const latRange = bounds.north - bounds.south;
    const lngRange = bounds.east - bounds.west;

    // Add padding
    const padding = 50;
    const usableWidth = mapWidth - (padding * 2);
    const usableHeight = mapHeight - (padding * 2);

    const x = padding + ((lng - bounds.west) / lngRange) * usableWidth;
    const y = padding + ((bounds.north - lat) / latRange) * usableHeight;

    return { x, y };
  };

  const renderSimpleMap = () => {
    const mapWidth = 800;
    const mapHeight = 600;

    return (
      <div className="relative border rounded-lg overflow-hidden" style={{ borderColor: colors.border }}>
        {/* Map background */}
        <div
          className="relative"
          style={{
            width: mapWidth,
            height: mapHeight,
            backgroundColor: '#e6f3ff',
            backgroundImage: `
              linear-gradient(45deg, #f0f8ff 25%, transparent 25%),
              linear-gradient(-45deg, #f0f8ff 25%, transparent 25%),
              linear-gradient(45deg, transparent 75%, #f0f8ff 75%),
              linear-gradient(-45deg, transparent 75%, #f0f8ff 75%)
            `,
            backgroundSize: '20px 20px',
            backgroundPosition: '0 0, 0 10px, 10px -10px, -10px 0px',
          }}
          onClick={(e) => {
            if (onMapClick && bounds) {
              const rect = e.currentTarget.getBoundingClientRect();
              const x = e.clientX - rect.left;
              const y = e.clientY - rect.top;
              
              // Convert pixel coordinates back to lat/lng
              const padding = 50;
              const usableWidth = mapWidth - (padding * 2);
              const usableHeight = mapHeight - (padding * 2);
              
              const lngRange = bounds.east - bounds.west;
              const latRange = bounds.north - bounds.south;
              
              const lng = bounds.west + ((x - padding) / usableWidth) * lngRange;
              const lat = bounds.north - ((y - padding) / usableHeight) * latRange;
              
              onMapClick(lat, lng);
            }
          }}
        >
          {/* Grid lines */}
          <svg className="absolute inset-0 pointer-events-none" width={mapWidth} height={mapHeight}>
            <defs>
              <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
                <path d="M 50 0 L 0 0 0 50" fill="none" stroke={colors.border} strokeWidth="1" opacity="0.3"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)" />
          </svg>

          {/* Markers */}
          {markers.map(marker => {
            const { x, y } = projectToPixels(marker.latitude, marker.longitude, mapWidth, mapHeight);
            
            return (
              <div
                key={marker.id}
                className="absolute cursor-pointer transform -translate-x-1/2 -translate-y-1/2 hover:scale-110 transition-transform"
                style={{ left: x, top: y }}
                onClick={(e) => {
                  e.stopPropagation();
                  onMarkerClick?.(marker);
                }}
                title={`${marker.title}\n${marker.description || ''}`}
              >
                {renderMarker ? renderMarker(marker) : (
                  <div
                    className="rounded-full border-2 border-white shadow-lg flex items-center justify-center"
                    style={{
                      width: Math.max(marker.size, 8),
                      height: Math.max(marker.size, 8),
                      backgroundColor: marker.color,
                    }}
                  >
                    <div className="w-2 h-2 bg-white rounded-full" />
                  </div>
                )}
              </div>
            );
          })}

          {/* Map controls */}
          <div className="absolute top-4 right-4 flex flex-col space-y-2">
            <button
              className="w-8 h-8 bg-white border rounded shadow hover:bg-gray-50 flex items-center justify-center"
              style={{ borderColor: colors.border, color: colors.text }}
              onClick={() => console.log('Zoom in')}
            >
              +
            </button>
            <button
              className="w-8 h-8 bg-white border rounded shadow hover:bg-gray-50 flex items-center justify-center"
              style={{ borderColor: colors.border, color: colors.text }}
              onClick={() => console.log('Zoom out')}
            >
              −
            </button>
          </div>

          {/* Legend */}
          <div 
            className="absolute bottom-4 left-4 bg-white border rounded p-3 shadow-lg max-w-xs"
            style={{ borderColor: colors.border }}
          >
            <h4 className="font-semibold text-sm mb-2" style={{ color: colors.text }}>
              Map Legend
            </h4>
            <div className="space-y-1">
              <div className="flex items-center space-x-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: colors.primary }}
                />
                <span className="text-xs" style={{ color: colors.textSecondary }}>
                  {markers.length} locations
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Map info */}
        <div 
          className="p-3 border-t text-sm"
          style={{ borderTopColor: colors.border, backgroundColor: colors.surface }}
        >
          <div className="flex justify-between items-center">
            <span style={{ color: colors.textSecondary }}>
              Showing {markers.length} markers
            </span>
            <span style={{ color: colors.textSecondary }}>
              {config.mapStyle || 'Simple'} view
            </span>
          </div>
        </div>
      </div>
    );
  };

  return (
    <BaseView {...baseProps} data={data} className={className}>
      <div className="p-6">
        {markers.length > 0 ? renderSimpleMap() : (
          <div className="text-center py-12">
            <div className="text-4xl mb-4">🗺️</div>
            <h3 className="text-lg font-semibold mb-2" style={{ color: colors.text }}>
              No Location Data
            </h3>
            <p className="text-sm" style={{ color: colors.textSecondary }}>
              No items have valid latitude and longitude coordinates to display on the map.
            </p>
          </div>
        )}
      </div>
    </BaseView>
  );
};

export default MapView;
