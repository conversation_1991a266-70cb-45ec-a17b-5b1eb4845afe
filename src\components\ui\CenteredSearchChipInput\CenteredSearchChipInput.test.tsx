import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import CenteredSearchChipInput from './CenteredSearchChipInput';
import type { ChipData, FilterOption } from './CenteredSearchChipInput';

// Mock the theme store
jest.mock('../../../stores/themeStore', () => ({
  useThemeStore: () => ({
    colors: {
      primary: '#2563eb',
      secondary: '#4f46e5',
      accent: '#7c3aed',
      warning: '#d97706',
      background: '#ffffff',
      surface: '#f9fafb',
      border: '#e5e7eb',
      text: '#111827',
      textSecondary: '#6b7280',
      textMuted: '#9ca3af',
      mutedForeground: '#64748b',
      hover: '#f1f5f9',
      shadow: 'rgba(0, 0, 0, 0.1)',
    },
  }),
}));

const mockFilterOptions: FilterOption[] = [
  { id: 'status', label: 'Status', category: 'default', type: 'select' },
  { id: 'priority', label: 'Priority', category: 'default', type: 'select' },
  { id: 'custom-field', label: 'Custom Field', category: 'custom', type: 'text' },
  { id: 'group-customer', label: 'Customer', category: 'group' },
  { id: 'fav-invoiced', label: 'Fully Invoiced', category: 'favorite' },
];

const mockChips: ChipData[] = [
  { id: '1', label: 'Active Status', type: 'default', removable: true },
  { id: '2', label: 'High Priority', type: 'default', removable: true },
];

describe('CenteredSearchChipInput', () => {
  const defaultProps = {
    placeholder: 'Search...',
    chips: [],
    filterOptions: mockFilterOptions,
    onSearch: jest.fn(),
    onChipAdd: jest.fn(),
    onChipRemove: jest.fn(),
    onClearAll: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with placeholder text', () => {
    render(<CenteredSearchChipInput {...defaultProps} />);
    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
  });

  it('renders existing chips', () => {
    render(<CenteredSearchChipInput {...defaultProps} chips={mockChips} />);
    expect(screen.getByText('Active Status')).toBeInTheDocument();
    expect(screen.getByText('High Priority')).toBeInTheDocument();
  });

  it('calls onSearch when typing in input', async () => {
    const user = userEvent.setup();
    render(<CenteredSearchChipInput {...defaultProps} />);
    
    const input = screen.getByPlaceholderText('Search...');
    await user.type(input, 'test query');
    
    expect(defaultProps.onSearch).toHaveBeenCalledWith('test query');
  });

  it('opens dropdown when input is focused', async () => {
    const user = userEvent.setup();
    render(<CenteredSearchChipInput {...defaultProps} />);
    
    const input = screen.getByPlaceholderText('Search...');
    await user.click(input);
    
    expect(screen.getByText('Filters')).toBeInTheDocument();
    expect(screen.getByText('Custom')).toBeInTheDocument();
    expect(screen.getByText('Group By')).toBeInTheDocument();
    expect(screen.getByText('Favorites')).toBeInTheDocument();
  });

  it('shows filter options when section is selected', async () => {
    const user = userEvent.setup();
    render(<CenteredSearchChipInput {...defaultProps} />);
    
    const input = screen.getByPlaceholderText('Search...');
    await user.click(input);
    
    const filtersTab = screen.getByText('Filters');
    await user.click(filtersTab);
    
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Priority')).toBeInTheDocument();
  });

  it('calls onChipAdd when filter option is selected', async () => {
    const user = userEvent.setup();
    render(<CenteredSearchChipInput {...defaultProps} />);
    
    const input = screen.getByPlaceholderText('Search...');
    await user.click(input);
    
    const filtersTab = screen.getByText('Filters');
    await user.click(filtersTab);
    
    const statusOption = screen.getByText('Status');
    await user.click(statusOption);
    
    expect(defaultProps.onChipAdd).toHaveBeenCalledWith(
      expect.objectContaining({
        label: 'Status',
        type: 'default',
        category: 'default',
        removable: true,
      })
    );
  });

  it('calls onChipRemove when chip remove button is clicked', async () => {
    const user = userEvent.setup();
    render(<CenteredSearchChipInput {...defaultProps} chips={mockChips} />);
    
    const removeButtons = screen.getAllByLabelText(/Remove/);
    await user.click(removeButtons[0]);
    
    expect(defaultProps.onChipRemove).toHaveBeenCalledWith('1');
  });

  it('shows clear all button when chips exist', () => {
    render(<CenteredSearchChipInput {...defaultProps} chips={mockChips} />);
    expect(screen.getByText('Clear All')).toBeInTheDocument();
  });

  it('calls onClearAll when clear all button is clicked', async () => {
    const user = userEvent.setup();
    render(<CenteredSearchChipInput {...defaultProps} chips={mockChips} />);
    
    const clearAllButton = screen.getByText('Clear All');
    await user.click(clearAllButton);
    
    expect(defaultProps.onClearAll).toHaveBeenCalled();
  });

  it('hides placeholder when chips exist', () => {
    render(<CenteredSearchChipInput {...defaultProps} chips={mockChips} />);
    const input = screen.getByRole('textbox');
    expect(input).toHaveAttribute('placeholder', '');
  });

  it('shows different chip colors based on type', () => {
    const mixedChips: ChipData[] = [
      { id: '1', label: 'Default', type: 'default', removable: true },
      { id: '2', label: 'Custom', type: 'custom', removable: true },
      { id: '3', label: 'Group', type: 'group', removable: true },
      { id: '4', label: 'Favorite', type: 'favorite', removable: true },
    ];
    
    render(<CenteredSearchChipInput {...defaultProps} chips={mixedChips} />);
    
    expect(screen.getByText('Default')).toBeInTheDocument();
    expect(screen.getByText('Custom')).toBeInTheDocument();
    expect(screen.getByText('Group')).toBeInTheDocument();
    expect(screen.getByText('Favorite')).toBeInTheDocument();
  });

  it('closes dropdown when clicking outside', async () => {
    const user = userEvent.setup();
    render(
      <div>
        <CenteredSearchChipInput {...defaultProps} />
        <div data-testid="outside">Outside element</div>
      </div>
    );
    
    const input = screen.getByPlaceholderText('Search...');
    await user.click(input);
    
    expect(screen.getByText('Filters')).toBeInTheDocument();
    
    const outsideElement = screen.getByTestId('outside');
    await user.click(outsideElement);
    
    await waitFor(() => {
      expect(screen.queryByText('Filters')).not.toBeInTheDocument();
    });
  });

  it('handles form submission', async () => {
    const user = userEvent.setup();
    render(<CenteredSearchChipInput {...defaultProps} />);
    
    const input = screen.getByPlaceholderText('Search...');
    await user.type(input, 'test query');
    await user.keyboard('{Enter}');
    
    expect(defaultProps.onSearch).toHaveBeenCalledWith('test query');
  });

  it('shows empty state message when no options available', async () => {
    const user = userEvent.setup();
    render(<CenteredSearchChipInput {...defaultProps} filterOptions={[]} />);
    
    const input = screen.getByPlaceholderText('Search...');
    await user.click(input);
    
    const filtersTab = screen.getByText('Filters');
    await user.click(filtersTab);
    
    expect(screen.getByText('No default options available')).toBeInTheDocument();
  });

  it('applies correct accessibility attributes', () => {
    render(<CenteredSearchChipInput {...defaultProps} chips={mockChips} data-testid="search-input" />);
    
    expect(screen.getByTestId('search-input')).toBeInTheDocument();
    
    const removeButtons = screen.getAllByLabelText(/Remove/);
    expect(removeButtons[0]).toHaveAttribute('aria-label', 'Remove Active Status');
  });
});
