import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import type { ViewConfig, ViewState, ViewContext, ViewFilter, ViewSort } from '../types/views';

export interface ViewStoreState {
  // Current view state
  currentViewId: string;
  availableViews: ViewConfig[];
  viewData: any[];
  isLoading: boolean;
  error?: string;
  
  // Search and filtering
  searchQuery: string;
  activeFilters: ViewFilter[];
  activeSorts: ViewSort[];
  
  // Pagination
  currentPage: number;
  pageSize: number;
  totalItems: number;
  
  // Selection
  selectedItems: string[];
  selectAll: boolean;
  
  // View-specific settings
  viewSettings: { [viewId: string]: any };
  
  // Actions
  setCurrentView: (viewId: string) => void;
  setAvailableViews: (views: ViewConfig[]) => void;
  setViewData: (data: any[]) => void;
  setLoading: (loading: boolean) => void;
  setError: (error?: string) => void;
  
  // Search and filtering actions
  setSearchQuery: (query: string) => void;
  addFilter: (filter: ViewFilter) => void;
  removeFilter: (filterId: string) => void;
  updateFilter: (filterId: string, value: any) => void;
  clearFilters: () => void;
  
  // Sorting actions
  setSorts: (sorts: ViewSort[]) => void;
  addSort: (sort: ViewSort) => void;
  removeSort: (field: string) => void;
  clearSorts: () => void;
  
  // Pagination actions
  setPage: (page: number) => void;
  setPageSize: (size: number) => void;
  setTotalItems: (total: number) => void;
  
  // Selection actions
  selectItem: (itemId: string) => void;
  deselectItem: (itemId: string) => void;
  selectItems: (itemIds: string[]) => void;
  deselectItems: (itemIds: string[]) => void;
  selectAllItems: () => void;
  deselectAllItems: () => void;
  toggleSelectAll: () => void;
  
  // View settings actions
  setViewSetting: (viewId: string, key: string, value: any) => void;
  getViewSetting: (viewId: string, key: string, defaultValue?: any) => any;
  clearViewSettings: (viewId: string) => void;
  
  // Data manipulation actions
  refreshData: () => void;
  exportData: (format: 'csv' | 'excel' | 'pdf') => Promise<void>;
  
  // Utility actions
  reset: () => void;
  getFilteredData: () => any[];
  getPaginatedData: () => any[];
}

const initialState = {
  currentViewId: 'list',
  availableViews: [],
  viewData: [],
  isLoading: false,
  error: undefined,
  searchQuery: '',
  activeFilters: [],
  activeSorts: [],
  currentPage: 1,
  pageSize: 20,
  totalItems: 0,
  selectedItems: [],
  selectAll: false,
  viewSettings: {},
};

export const useViewStore = create<ViewStoreState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        // Basic state setters
        setCurrentView: (viewId: string) => {
          set({ currentViewId: viewId });
        },

        setAvailableViews: (views: ViewConfig[]) => {
          set({ availableViews: views });
        },

        setViewData: (data: any[]) => {
          set({ 
            viewData: data,
            totalItems: data.length,
            selectedItems: [],
            selectAll: false,
          });
        },

        setLoading: (loading: boolean) => {
          set({ isLoading: loading });
        },

        setError: (error?: string) => {
          set({ error });
        },

        // Search and filtering
        setSearchQuery: (query: string) => {
          set({ 
            searchQuery: query,
            currentPage: 1, // Reset to first page when searching
          });
        },

        addFilter: (filter: ViewFilter) => {
          set(state => ({
            activeFilters: [...state.activeFilters.filter(f => f.id !== filter.id), filter],
            currentPage: 1,
          }));
        },

        removeFilter: (filterId: string) => {
          set(state => ({
            activeFilters: state.activeFilters.filter(f => f.id !== filterId),
            currentPage: 1,
          }));
        },

        updateFilter: (filterId: string, value: any) => {
          set(state => ({
            activeFilters: state.activeFilters.map(f => 
              f.id === filterId ? { ...f, value } : f
            ),
            currentPage: 1,
          }));
        },

        clearFilters: () => {
          set({ 
            activeFilters: [],
            currentPage: 1,
          });
        },

        // Sorting
        setSorts: (sorts: ViewSort[]) => {
          set({ activeSorts: sorts });
        },

        addSort: (sort: ViewSort) => {
          set(state => ({
            activeSorts: [...state.activeSorts.filter(s => s.field !== sort.field), sort],
          }));
        },

        removeSort: (field: string) => {
          set(state => ({
            activeSorts: state.activeSorts.filter(s => s.field !== field),
          }));
        },

        clearSorts: () => {
          set({ activeSorts: [] });
        },

        // Pagination
        setPage: (page: number) => {
          set({ currentPage: page });
        },

        setPageSize: (size: number) => {
          set({ 
            pageSize: size,
            currentPage: 1, // Reset to first page when changing page size
          });
        },

        setTotalItems: (total: number) => {
          set({ totalItems: total });
        },

        // Selection
        selectItem: (itemId: string) => {
          set(state => ({
            selectedItems: [...state.selectedItems, itemId],
            selectAll: false,
          }));
        },

        deselectItem: (itemId: string) => {
          set(state => ({
            selectedItems: state.selectedItems.filter(id => id !== itemId),
            selectAll: false,
          }));
        },

        selectItems: (itemIds: string[]) => {
          set(state => ({
            selectedItems: [...new Set([...state.selectedItems, ...itemIds])],
            selectAll: false,
          }));
        },

        deselectItems: (itemIds: string[]) => {
          set(state => ({
            selectedItems: state.selectedItems.filter(id => !itemIds.includes(id)),
            selectAll: false,
          }));
        },

        selectAllItems: () => {
          const filteredData = get().getFilteredData();
          const allIds = filteredData.map(item => item.id).filter(Boolean);
          set({ 
            selectedItems: allIds,
            selectAll: true,
          });
        },

        deselectAllItems: () => {
          set({ 
            selectedItems: [],
            selectAll: false,
          });
        },

        toggleSelectAll: () => {
          const state = get();
          if (state.selectAll || state.selectedItems.length > 0) {
            state.deselectAllItems();
          } else {
            state.selectAllItems();
          }
        },

        // View settings
        setViewSetting: (viewId: string, key: string, value: any) => {
          set(state => ({
            viewSettings: {
              ...state.viewSettings,
              [viewId]: {
                ...state.viewSettings[viewId],
                [key]: value,
              },
            },
          }));
        },

        getViewSetting: (viewId: string, key: string, defaultValue?: any) => {
          const state = get();
          return state.viewSettings[viewId]?.[key] ?? defaultValue;
        },

        clearViewSettings: (viewId: string) => {
          set(state => ({
            viewSettings: {
              ...state.viewSettings,
              [viewId]: {},
            },
          }));
        },

        // Data manipulation
        refreshData: () => {
          // This would typically trigger a data fetch
          set({ isLoading: true });
          // Implementation would depend on data source
        },

        exportData: async (format: 'csv' | 'excel' | 'pdf') => {
          const data = get().getFilteredData();
          // Implementation would depend on export library
          console.log(`Exporting ${data.length} items as ${format}`);
        },

        // Utility functions
        reset: () => {
          set(initialState);
        },

        getFilteredData: () => {
          const state = get();
          let filteredData = [...state.viewData];

          // Apply search query
          if (state.searchQuery) {
            const query = state.searchQuery.toLowerCase();
            filteredData = filteredData.filter(item =>
              Object.values(item).some(value =>
                String(value).toLowerCase().includes(query)
              )
            );
          }

          // Apply filters
          state.activeFilters.forEach(filter => {
            if (filter.value !== undefined && filter.value !== null && filter.value !== '') {
              filteredData = filteredData.filter(item => {
                const itemValue = item[filter.id];
                switch (filter.type) {
                  case 'text':
                    return String(itemValue).toLowerCase().includes(String(filter.value).toLowerCase());
                  case 'select':
                    return itemValue === filter.value;
                  case 'date':
                    // Simple date comparison - could be enhanced
                    return new Date(itemValue).toDateString() === new Date(filter.value).toDateString();
                  case 'number':
                    return Number(itemValue) === Number(filter.value);
                  case 'boolean':
                    return Boolean(itemValue) === Boolean(filter.value);
                  default:
                    return itemValue === filter.value;
                }
              });
            }
          });

          // Apply sorting
          if (state.activeSorts.length > 0) {
            filteredData.sort((a, b) => {
              for (const sort of state.activeSorts) {
                const aValue = a[sort.field];
                const bValue = b[sort.field];
                
                let comparison = 0;
                if (aValue < bValue) comparison = -1;
                else if (aValue > bValue) comparison = 1;
                
                if (comparison !== 0) {
                  return sort.direction === 'desc' ? -comparison : comparison;
                }
              }
              return 0;
            });
          }

          return filteredData;
        },

        getPaginatedData: () => {
          const state = get();
          const filteredData = state.getFilteredData();
          const startIndex = (state.currentPage - 1) * state.pageSize;
          const endIndex = startIndex + state.pageSize;
          
          return filteredData.slice(startIndex, endIndex);
        },
      }),
      {
        name: 'view-store',
        partialize: (state) => ({
          currentViewId: state.currentViewId,
          pageSize: state.pageSize,
          viewSettings: state.viewSettings,
        }),
      }
    ),
    {
      name: 'ViewStore',
    }
  )
);
